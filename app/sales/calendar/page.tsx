"use client"

import { useState, useEffect } from 'react'
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth, isSameDay, addMonths, subMonths } from 'date-fns'
import { ChevronLeft, ChevronRight, Plus, ArrowLeft } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { getSalesPlans, getProduk } from '@/lib/directus'
import Link from 'next/link'
import { useRouter } from 'next/navigation'

interface SalesPlan {
  id: string
  date: string
  kangider_id: string
  produk_id: string
  quantity: number
  status: string
  notes?: string
}

interface Product {
  id: string
  nama_produk: string
}

export default function SalesCalendarPage() {
  const router = useRouter()
  const [currentDate, setCurrentDate] = useState(new Date())
  const [salesPlans, setSalesPlans] = useState<SalesPlan[]>([])
  const [products, setProducts] = useState<Product[]>([])
  const [isLoading, setIsLoading] = useState(true)

  const monthStart = startOfMonth(currentDate)
  const monthEnd = endOfMonth(currentDate)
  const calendarDays = eachDayOfInterval({ start: monthStart, end: monthEnd })

  useEffect(() => {
    fetchData()
  }, [currentDate])

  const fetchData = async () => {
    try {
      setIsLoading(true)
      const startDate = format(monthStart, 'yyyy-MM-dd')
      const endDate = format(monthEnd, 'yyyy-MM-dd')
      
      const [salesData, productsData] = await Promise.all([
        getSalesPlans({
          date: {
            _between: [startDate, endDate]
          }
        }),
        getProduk()
      ])

      setSalesPlans(salesData || [])
      setProducts(productsData || [])
    } catch (error) {
      console.error('Error fetching data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getSalesForDate = (date: Date) => {
    const dateStr = format(date, 'yyyy-MM-dd')
    return salesPlans.filter(plan => plan.date === dateStr)
  }

  const getProductName = (productId: string) => {
    const product = products.find(p => p.id === productId)
    return product?.nama_produk || 'Unknown Product'
  }

  const getTotalQuantityForDate = (date: Date) => {
    const sales = getSalesForDate(date)
    return sales.reduce((total, sale) => total + sale.quantity, 0)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'planned': return 'bg-blue-100 text-blue-800'
      case 'in_progress': return 'bg-yellow-100 text-yellow-800'
      case 'completed': return 'bg-green-100 text-green-800'
      case 'cancelled': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => direction === 'prev' ? subMonths(prev, 1) : addMonths(prev, 1))
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-lg font-semibold">Loading calendar...</h2>
          <p className="text-muted-foreground">Please wait</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Sales Calendar</h1>
            <p className="text-muted-foreground">Monthly view of sales plans</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button asChild>
            <Link href="/sales/add">
              <Plus className="mr-2 h-4 w-4" />
              Create Sales Plan
            </Link>
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>{format(currentDate, 'MMMM yyyy')}</CardTitle>
              <CardDescription>Sales plans for the month</CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigateMonth('prev')}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentDate(new Date())}
              >
                Today
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigateMonth('next')}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-7 gap-1 mb-4">
            {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
              <div key={day} className="p-2 text-center font-medium text-sm text-muted-foreground">
                {day}
              </div>
            ))}
          </div>
          
          <div className="grid grid-cols-7 gap-1">
            {calendarDays.map(day => {
              const salesForDay = getSalesForDate(day)
              const totalQuantity = getTotalQuantityForDate(day)
              const isToday = isSameDay(day, new Date())
              const isCurrentMonth = isSameMonth(day, currentDate)
              
              return (
                <div
                  key={day.toISOString()}
                  className={`
                    min-h-[120px] p-2 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors
                    ${isToday ? 'bg-blue-50 border-blue-200' : 'border-gray-200'}
                    ${!isCurrentMonth ? 'opacity-50' : ''}
                  `}
                  onClick={() => router.push(`/sales/add?date=${format(day, 'yyyy-MM-dd')}`)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <span className={`text-sm font-medium ${isToday ? 'text-blue-600' : 'text-gray-900'}`}>
                      {format(day, 'd')}
                    </span>
                    {totalQuantity > 0 && (
                      <Badge variant="secondary" className="text-xs">
                        {totalQuantity}
                      </Badge>
                    )}
                  </div>
                  
                  <div className="space-y-1">
                    {salesForDay.slice(0, 3).map(sale => (
                      <div
                        key={sale.id}
                        className={`text-xs p-1 rounded truncate ${getStatusColor(sale.status)}`}
                        title={`${getProductName(sale.produk_id)} - ${sale.quantity} units`}
                      >
                        {getProductName(sale.produk_id).substring(0, 15)}
                        {getProductName(sale.produk_id).length > 15 ? '...' : ''}
                      </div>
                    ))}
                    {salesForDay.length > 3 && (
                      <div className="text-xs text-muted-foreground">
                        +{salesForDay.length - 3} more
                      </div>
                    )}
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Legend */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Status Legend</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4">
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 rounded bg-blue-100 border border-blue-200"></div>
              <span className="text-sm">Planned</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 rounded bg-yellow-100 border border-yellow-200"></div>
              <span className="text-sm">In Progress</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 rounded bg-green-100 border border-green-200"></div>
              <span className="text-sm">Completed</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 rounded bg-red-100 border border-red-200"></div>
              <span className="text-sm">Cancelled</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
