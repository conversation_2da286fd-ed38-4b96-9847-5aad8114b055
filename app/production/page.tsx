"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import {
  Coffee,
  Plus,
  Pencil,
  Trash2,
  Package,
  Truck,
  RotateCcw,
  BarChart3,
  CheckCircle,
  Clock,
  Calendar
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { getProduk, getDailyProduction, deleteDailyProduction } from "@/lib/directus"
import { getProductionStock, getDistributions, getDistributionChannels } from "@/lib/production-api"
import { format } from "date-fns"
import { useToast } from "@/components/ui/use-toast"
import { Produk } from "@/lib/directus"
import type { ProductionStock, ProductionDistribution, DistributionChannel } from "@/lib/production-types"

export default function ProductionPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [products, setProducts] = useState<Produk[]>([])
  const [todayProduction, setTodayProduction] = useState<any[]>([])
  const [productionStock, setProductionStock] = useState<ProductionStock[]>([])
  const [distributions, setDistributions] = useState<ProductionDistribution[]>([])
  const [distributionChannels, setDistributionChannels] = useState<DistributionChannel[]>([])
  const [activeTab, setActiveTab] = useState("overview")
  const [syncStatus, setSyncStatus] = useState<any>(null)
  const [syncingIds, setSyncingIds] = useState<Set<string>>(new Set())
  const [badStockData, setBadStockData] = useState<any[]>([])

  // Helper function to safely format dates
  const safeFormatDate = (dateValue: any, formatString: string, fallback: string = 'N/A') => {
    if (!dateValue) return fallback
    try {
      const date = new Date(dateValue)
      if (isNaN(date.getTime())) return fallback
      return format(date, formatString)
    } catch (error) {
      console.warn('Invalid date value:', dateValue)
      return fallback
    }
  }

  // Helper function to get distribution channel name
  const getChannelName = (channelId: string) => {
    const channel = distributionChannels.find(c => c.id === channelId)
    return channel ? `${channel.name} (${channel.type})` : channelId
  }

  // Helper function to calculate bad stock (H+3 from production date)
  const calculateBadStock = () => {
    const today = new Date()
    const badStock = []

    for (const stock of productionStock) {
      if (stock.available_quantity > 0) {
        // Get the latest production date for this product
        const productProductions = todayProduction.filter(p => p.produk_id === stock.produk_id)

        if (productProductions.length > 0) {
          // Find the latest production date
          const latestProduction = productProductions.reduce((latest, current) => {
            const currentDate = new Date(current.date || current.created_at)
            const latestDate = new Date(latest.date || latest.created_at)
            return currentDate > latestDate ? current : latest
          })

          const productionDate = new Date(latestProduction.date || latestProduction.created_at)
          const daysDiff = Math.floor((today.getTime() - productionDate.getTime()) / (1000 * 60 * 60 * 24))

          if (daysDiff >= 3) {
            const product = products.find(p => p.id === stock.produk_id)
            badStock.push({
              ...stock,
              product_name: product?.nama_produk || 'Unknown Product',
              production_date: productionDate,
              days_old: daysDiff,
              status: daysDiff >= 7 ? 'critical' : daysDiff >= 5 ? 'warning' : 'attention'
            })
          }
        }
      }
    }

    return badStock.sort((a, b) => b.days_old - a.days_old)
  }
  const [isLoadingSyncStatus, setIsLoadingSyncStatus] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    async function initializeData() {
      setIsLoading(true)
      try {
        await Promise.all([fetchData(), loadSyncStatus()])
      } finally {
        setIsLoading(false)
      }
    }

    initializeData()
  }, [toast])

  // Calculate bad stock when data changes
  useEffect(() => {
    if (productionStock.length > 0 && products.length > 0) {
      const badStock = calculateBadStock()
      setBadStockData(badStock)
    }
  }, [productionStock, products, todayProduction])

  // Load sync status
  const loadSyncStatus = async () => {
    setIsLoadingSyncStatus(true)
    try {
      const today = new Date().toISOString().split("T")[0]
      const token = localStorage.getItem('directus_token')

      const response = await fetch(`/api/production/daily/sync?date=${today}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const result = await response.json()
        setSyncStatus(result.data)
      }
    } catch (error) {
      console.error("Error loading sync status:", error)
    } finally {
      setIsLoadingSyncStatus(false)
    }
  }

  // Sync daily production to stock
  const handleSyncToStock = async (dailyProductionId: string) => {
    try {
      // Add to syncing set
      setSyncingIds(prev => new Set(prev).add(dailyProductionId))

      const token = localStorage.getItem('directus_token')

      const response = await fetch('/api/production/daily/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          daily_production_id: dailyProductionId
        })
      })

      if (response.ok) {
        const result = await response.json()
        toast({
          title: "Success",
          description: result.message,
        })

        // Reload data to get updated sync status
        await Promise.all([fetchData(), loadSyncStatus()])
      } else {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to sync to stock')
      }
    } catch (error) {
      console.error("Error syncing to stock:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to sync to stock",
        variant: "destructive"
      })
    } finally {
      // Remove from syncing set
      setSyncingIds(prev => {
        const newSet = new Set(prev)
        newSet.delete(dailyProductionId)
        return newSet
      })
    }
  }

  const fetchData = async () => {
    try {
      const today = new Date().toISOString().split("T")[0]

      const [
        productsData,
        productionData,
        stockData,
        distributionsData,
        channelsData
      ] = await Promise.all([
        getProduk(),
        getDailyProduction({
          date: { _eq: today }
        }),
        getProductionStock(),
        getDistributions(today),
        getDistributionChannels()
      ])

      setProducts(productsData)
      setTodayProduction(productionData)
      setProductionStock(stockData)
      setDistributions(distributionsData)
      setDistributionChannels(channelsData)
    } catch (error) {
      console.error("Error fetching production data:", error)
      toast({
        title: "Error",
        description: "Failed to load production data",
        variant: "destructive"
      })
    }
  }

  const handleDelete = async (id: string) => {
    try {
      await deleteDailyProduction(id)
      setTodayProduction(prev => prev.filter(prod => prod.id !== id))
      toast({
        title: "Success",
        description: "Production record deleted successfully"
      })
    } catch (error) {
      console.error("Error deleting production:", error)
      toast({
        title: "Error",
        description: "Failed to delete production record",
        variant: "destructive"
      })
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-lg font-semibold">Loading production data...</h2>
          <p className="text-muted-foreground">Please wait</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Production Management</h1>
          <p className="text-muted-foreground">Comprehensive production tracking and distribution management</p>
        </div>
        <div className="flex gap-2">
          <Button asChild>
            <Link href="/production/add">
              <Plus className="mr-2 h-4 w-4" />
              Record Production
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/sales">
              <Truck className="mr-2 h-4 w-4" />
              Sales Plan
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/production/distribution/add">
              <Truck className="mr-2 h-4 w-4" />
              Other Distribution
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/production/returns/add">
              <RotateCcw className="mr-2 h-4 w-4" />
              Return Stock
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/production/analytics">
              <BarChart3 className="mr-2 h-4 w-4" />
              Analytics
            </Link>
          </Button>
          {distributionChannels.length === 0 && (
            <Button
              variant="outline"
              onClick={async () => {
                try {
                  const token = localStorage.getItem('directus_token')
                  const response = await fetch('/api/setup-distribution-channels', {
                    method: 'POST',
                    headers: {
                      'Authorization': `Bearer ${token}`
                    }
                  })

                  if (response.ok) {
                    const result = await response.json()
                    toast({
                      title: "Success",
                      description: result.message,
                    })
                    await fetchData() // Reload data
                  } else {
                    throw new Error('Failed to setup distribution channels')
                  }
                } catch (error) {
                  toast({
                    title: "Error",
                    description: "Failed to setup distribution channels",
                    variant: "destructive"
                  })
                }
              }}
            >
              Setup Channels
            </Button>
          )}
        </div>
      </div>

      {/* Dashboard Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Today's Production</CardTitle>
            <Coffee className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {todayProduction.reduce((sum, prod) => sum + prod.quantity_produced, 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              {todayProduction.length} products produced
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Available Stock</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {productionStock.reduce((sum, stock) => sum + stock.available_quantity, 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              Ready for distribution
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Distributed Today</CardTitle>
            <Truck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {distributions.filter(d => d.status === 'distributed').reduce((sum, dist) => sum + dist.quantity, 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              {distributions.filter(d => d.status === 'distributed').length} distributions
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Returns Today</CardTitle>
            <RotateCcw className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {distributions.reduce((sum, dist) => sum + (dist.returned_quantity || 0), 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              Items returned
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="production">Production</TabsTrigger>
          <TabsTrigger value="stock">Stock</TabsTrigger>
          <TabsTrigger value="distribution">Distribution</TabsTrigger>
          <TabsTrigger value="returns">Returns</TabsTrigger>
          <TabsTrigger value="badstock">Bad Stock</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Today's Production Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Today's Production Summary</CardTitle>
                <CardDescription>{format(new Date(), "dd MMMM yyyy")}</CardDescription>
              </CardHeader>
              <CardContent>
                {todayProduction.length === 0 ? (
                  <div className="text-center py-6">
                    <p className="text-muted-foreground">No production recorded today</p>
                    <Button className="mt-4" asChild>
                      <Link href="/production/add">
                        <Plus className="mr-2 h-4 w-4" />
                        Record Production
                      </Link>
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {todayProduction.map((production) => {
                      const product = products.find(p => p.id === production.produk_id)
                      return (
                        <div key={production.id} className="flex items-center justify-between p-3 border rounded-lg">
                          <div>
                            <p className="font-medium">{product?.nama_produk || `Product ${production.produk_id}`}</p>
                            <p className="text-sm text-muted-foreground">
                              Target: {production.target_quantity || "-"}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="text-lg font-bold">{production.quantity_produced}</p>
                            <p className="text-sm text-muted-foreground">produced</p>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Stock Status */}
            <Card>
              <CardHeader>
                <CardTitle>Current Stock Status</CardTitle>
                <CardDescription>Available for distribution</CardDescription>
              </CardHeader>
              <CardContent>
                {productionStock.length === 0 ? (
                  <div className="text-center py-6">
                    <p className="text-muted-foreground">No stock data available</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {productionStock.map((stock) => {
                      const product = products.find(p => p.id === stock.produk_id)
                      return (
                        <div key={stock.id} className="flex items-center justify-between p-3 border rounded-lg">
                          <div>
                            <p className="font-medium">{product?.nama_produk || `Product ${stock.produk_id}`}</p>
                            <p className="text-sm text-muted-foreground">
                              Reserved: {stock.reserved_quantity}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="text-lg font-bold text-green-600">{stock.available_quantity}</p>
                            <p className="text-sm text-muted-foreground">available</p>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Recent Distributions */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Distributions</CardTitle>
              <CardDescription>Latest distribution activities</CardDescription>
            </CardHeader>
            <CardContent>
              {distributions.length === 0 ? (
                <div className="text-center py-6">
                  <p className="text-muted-foreground">No distributions today</p>
                  <Button className="mt-4" variant="outline" asChild>
                    <Link href="/production/distribution/add">
                      <Truck className="mr-2 h-4 w-4" />
                      Create Distribution
                    </Link>
                  </Button>
                </div>
              ) : (
                <div className="space-y-3">
                  {distributions.slice(0, 5).map((distribution) => {
                    const product = products.find(p => p.id === distribution.produk_id)
                    return (
                      <div key={distribution.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <p className="font-medium">{product?.nama_produk || `Product ${distribution.produk_id}`}</p>
                          <p className="text-sm text-muted-foreground">
                            Channel: {getChannelName(distribution.distribution_channel_id)}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-lg font-bold">{distribution.quantity}</p>
                          <Badge variant={
                            distribution.status === 'distributed' ? 'default' :
                            distribution.status === 'planned' ? 'secondary' :
                            distribution.status === 'returned' ? 'destructive' : 'outline'
                          }>
                            {distribution.status}
                          </Badge>
                        </div>
                      </div>
                    )
                  })}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Production Tab */}
        <TabsContent value="production" className="space-y-4">
          {/* Sync Status Card */}
          {syncStatus && (
            <Card>
              <CardHeader>
                <CardTitle>Production to Stock Sync Status</CardTitle>
                <CardDescription>Sync daily production to production stock</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold">{syncStatus.total}</div>
                    <p className="text-sm text-muted-foreground">Total Productions</p>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{syncStatus.synced}</div>
                    <p className="text-sm text-muted-foreground">Synced to Stock</p>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">{syncStatus.unsynced}</div>
                    <p className="text-sm text-muted-foreground">Pending Sync</p>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-600">{syncStatus.no_production}</div>
                    <p className="text-sm text-muted-foreground">No Production</p>
                  </div>
                </div>

                {syncStatus.unsynced > 0 && (
                  <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
                    <p className="text-sm text-orange-800">
                      <strong>Action Required:</strong> {syncStatus.unsynced} production(s) need to be synced to stock.
                      Use the "Sync to Stock" buttons below.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          <Card>
            <CardHeader>
              <CardTitle>Today's Production ({format(new Date(), "dd MMMM yyyy")})</CardTitle>
              <CardDescription>Production quantities for all products today</CardDescription>
            </CardHeader>
            <CardContent>
              {products.length === 0 ? (
                <div className="text-center py-6">
                  <p className="text-muted-foreground">No products found. Please add products in Directus first.</p>
                </div>
              ) : todayProduction.length === 0 ? (
                <div className="text-center py-6">
                  <p className="text-muted-foreground">No production data recorded for today.</p>
                  <Button className="mt-4" asChild>
                    <Link href="/production/add">
                      <Plus className="mr-2 h-4 w-4" />
                      Record Production
                    </Link>
                  </Button>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Product</TableHead>
                      <TableHead>Quantity Produced</TableHead>
                      <TableHead>Target</TableHead>
                      <TableHead>Progress</TableHead>
                      <TableHead>Sync Status</TableHead>
                      <TableHead>Notes</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {todayProduction.map((production) => {
                      const product = products.find(p => p.id === production.produk_id)
                      const progress = production.target_quantity ?
                        Math.round((production.quantity_produced / production.target_quantity) * 100) : 0
                      const syncInfo = syncStatus?.productions?.find((p: any) => p.id === production.id)

                      return (
                        <TableRow key={production.id}>
                          <TableCell className="font-medium">
                            {product?.nama_produk || `Product ${production.produk_id}`}
                          </TableCell>
                          <TableCell>{production.quantity_produced}</TableCell>
                          <TableCell>{production.target_quantity || "-"}</TableCell>
                          <TableCell>
                            {production.target_quantity ? (
                              <div className="flex items-center gap-2">
                                <div className="w-16 bg-gray-200 rounded-full h-2">
                                  <div
                                    className={`h-2 rounded-full ${
                                      progress >= 100 ? 'bg-green-500' :
                                      progress >= 80 ? 'bg-yellow-500' : 'bg-red-500'
                                    }`}
                                    style={{ width: `${Math.min(progress, 100)}%` }}
                                  ></div>
                                </div>
                                <span className="text-sm">{progress}%</span>
                              </div>
                            ) : "-"}
                          </TableCell>
                          <TableCell>
                            {syncInfo ? (
                              syncInfo.synced_to_stock ? (
                                <Badge variant="default" className="bg-green-50 text-green-700 border-green-200">
                                  <CheckCircle className="h-3 w-3 mr-1" />
                                  Synced
                                </Badge>
                              ) : syncInfo.can_sync ? (
                                <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
                                  <Clock className="h-3 w-3 mr-1" />
                                  Pending
                                </Badge>
                              ) : (
                                <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
                                  No Production
                                </Badge>
                              )
                            ) : (
                              <Badge variant="outline">Unknown</Badge>
                            )}
                          </TableCell>
                          <TableCell>{production.notes || "-"}</TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end gap-2">
                              {/* Sync to Stock button */}
                              {syncInfo?.can_sync && (
                                <Button
                                  variant="default"
                                  size="sm"
                                  onClick={() => handleSyncToStock(production.id)}
                                  disabled={syncingIds.has(production.id)}
                                >
                                  <Package className="h-4 w-4 mr-1" />
                                  {syncingIds.has(production.id) ? 'Syncing...' : 'Sync to Stock'}
                                </Button>
                              )}

                              <Button variant="outline" size="icon" asChild>
                                <Link href={`/production/edit/${production.id}`}>
                                  <Pencil className="h-4 w-4" />
                                </Link>
                              </Button>
                              <Button
                                variant="outline"
                                size="icon"
                                className="text-destructive"
                                onClick={() => handleDelete(production.id)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Stock Tab */}
        <TabsContent value="stock" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Production Stock Overview</CardTitle>
              <CardDescription>Current stock levels and availability</CardDescription>
            </CardHeader>
            <CardContent>
              {productionStock.length === 0 ? (
                <div className="text-center py-6">
                  <p className="text-muted-foreground">No stock data available</p>
                  <Button className="mt-4" variant="outline" asChild>
                    <Link href="/api/setup-production-stock">
                      <Package className="mr-2 h-4 w-4" />
                      Initialize Stock
                    </Link>
                  </Button>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Product</TableHead>
                      <TableHead>Total Stock</TableHead>
                      <TableHead>Reserved</TableHead>
                      <TableHead>Available</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Last Updated</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {productionStock.map((stock) => {
                      const product = products.find(p => p.id === stock.produk_id)
                      const stockStatus = stock.available_quantity === 0 ? 'empty' :
                                         stock.available_quantity < 10 ? 'low' : 'good'
                      return (
                        <TableRow key={stock.id}>
                          <TableCell className="font-medium">
                            {product?.nama_produk || `Product ${stock.produk_id}`}
                          </TableCell>
                          <TableCell>{stock.quantity}</TableCell>
                          <TableCell>{stock.reserved_quantity}</TableCell>
                          <TableCell className="font-bold">
                            {stock.available_quantity}
                          </TableCell>
                          <TableCell>
                            <Badge variant={
                              stockStatus === 'good' ? 'default' :
                              stockStatus === 'low' ? 'secondary' : 'destructive'
                            }>
                              {stockStatus === 'good' ? 'Good' :
                               stockStatus === 'low' ? 'Low Stock' : 'Empty'}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {safeFormatDate(stock.last_updated, "dd/MM HH:mm")}
                          </TableCell>
                          <TableCell className="text-right">
                            <Button variant="outline" size="sm" asChild>
                              <Link href={`/production/stock/adjust/${stock.id}`}>
                                <Pencil className="mr-1 h-4 w-4" />
                                Adjust
                              </Link>
                            </Button>
                          </TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Distribution Tab */}
        <TabsContent value="distribution" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Distribution Management</CardTitle>
                  <CardDescription>Track product distributions to various channels</CardDescription>
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" asChild>
                    <Link href="/production/distribution/history">
                      <Calendar className="mr-2 h-4 w-4" />
                      History
                    </Link>
                  </Button>
                  <Button asChild>
                    <Link href="/production/distribution/add">
                      <Plus className="mr-2 h-4 w-4" />
                      New Distribution
                    </Link>
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {distributions.length === 0 ? (
                <div className="text-center py-6">
                  <p className="text-muted-foreground">No distributions recorded today</p>
                  <Button className="mt-4" asChild>
                    <Link href="/production/distribution/add">
                      <Truck className="mr-2 h-4 w-4" />
                      Create Distribution
                    </Link>
                  </Button>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Product</TableHead>
                      <TableHead>Channel</TableHead>
                      <TableHead>Quantity</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Returned</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {distributions.map((distribution) => {
                      const product = products.find(p => p.id === distribution.produk_id)
                      return (
                        <TableRow key={distribution.id}>
                          <TableCell className="font-medium">
                            {product?.nama_produk || `Product ${distribution.produk_id}`}
                          </TableCell>
                          <TableCell>
                            {getChannelName(distribution.distribution_channel_id)}
                          </TableCell>
                          <TableCell>{distribution.quantity}</TableCell>
                          <TableCell>
                            <Badge variant={
                              distribution.status === 'distributed' ? 'default' :
                              distribution.status === 'planned' ? 'secondary' :
                              distribution.status === 'returned' ? 'destructive' : 'outline'
                            }>
                              {distribution.status}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {distribution.returned_quantity || 0}
                          </TableCell>
                          <TableCell>
                            {safeFormatDate(distribution.date, "dd/MM/yyyy")}
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end gap-2">
                              {distribution.status === 'planned' && (
                                <Button variant="outline" size="sm">
                                  <Truck className="mr-1 h-4 w-4" />
                                  Distribute
                                </Button>
                              )}
                              {distribution.status === 'distributed' && (
                                <Button variant="outline" size="sm" asChild>
                                  <Link href={`/production/returns/add?distribution=${distribution.id}`}>
                                    <RotateCcw className="mr-1 h-4 w-4" />
                                    Return
                                  </Link>
                                </Button>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Returns Tab */}
        <TabsContent value="returns" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Return Management</CardTitle>
                  <CardDescription>Handle returned products and restocking</CardDescription>
                </div>
                <Button asChild>
                  <Link href="/production/returns/add">
                    <Plus className="mr-2 h-4 w-4" />
                    Process Return
                  </Link>
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-center py-6">
                <p className="text-muted-foreground">Return management functionality coming soon</p>
                <p className="text-sm text-muted-foreground mt-2">
                  This will include return processing, condition assessment, and restocking decisions
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Bad Stock Tab */}
        <TabsContent value="badstock" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Bad Stock Monitoring</CardTitle>
              <CardDescription>Monitor products that are H+3 or older from production date</CardDescription>
            </CardHeader>
            <CardContent>
              {badStockData.length === 0 ? (
                <div className="text-center py-6">
                  <p className="text-muted-foreground">No bad stock detected</p>
                  <p className="text-sm text-muted-foreground mt-2">
                    All products are within the 3-day freshness window
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <Card>
                      <CardContent className="p-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-red-600">
                            {badStockData.filter(item => item.status === 'critical').length}
                          </div>
                          <p className="text-sm text-muted-foreground">Critical (7+ days)</p>
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-orange-600">
                            {badStockData.filter(item => item.status === 'warning').length}
                          </div>
                          <p className="text-sm text-muted-foreground">Warning (5-6 days)</p>
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-yellow-600">
                            {badStockData.filter(item => item.status === 'attention').length}
                          </div>
                          <p className="text-sm text-muted-foreground">Attention (3-4 days)</p>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Product</TableHead>
                        <TableHead>Available Quantity</TableHead>
                        <TableHead>Production Date</TableHead>
                        <TableHead>Days Old</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {badStockData.map((item) => (
                        <TableRow key={item.id}>
                          <TableCell className="font-medium">
                            {item.product_name}
                          </TableCell>
                          <TableCell>{item.available_quantity}</TableCell>
                          <TableCell>
                            {safeFormatDate(item.production_date, "dd/MM/yyyy")}
                          </TableCell>
                          <TableCell>
                            <span className={`font-medium ${
                              item.days_old >= 7 ? 'text-red-600' :
                              item.days_old >= 5 ? 'text-orange-600' : 'text-yellow-600'
                            }`}>
                              {item.days_old} days
                            </span>
                          </TableCell>
                          <TableCell>
                            <Badge variant={
                              item.status === 'critical' ? 'destructive' :
                              item.status === 'warning' ? 'secondary' : 'outline'
                            }>
                              {item.status === 'critical' ? 'Critical' :
                               item.status === 'warning' ? 'Warning' : 'Attention'}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end gap-2">
                              <Button variant="outline" size="sm" asChild>
                                <Link href={`/production/distribution/add?product=${item.produk_id}`}>
                                  <Truck className="mr-1 h-4 w-4" />
                                  Distribute
                                </Link>
                              </Button>
                              <Button variant="outline" size="sm" asChild>
                                <Link href={`/production/returns/add?product=${item.produk_id}&reason=expired`}>
                                  <RotateCcw className="mr-1 h-4 w-4" />
                                  Mark as Expired
                                </Link>
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

