import { NextResponse } from "next/server";
import directus from "@/lib/directus";
import { readItems, createItem } from "@directus/sdk";

export async function POST(request: Request) {
  try {
    console.log('🔧 Setting up distribution channels...');
    
    // Sample distribution channels data
    const sampleChannels = [
      {
        name: '<PERSON>',
        type: 'kangider',
        contact_person: '<PERSON>',
        phone: '081234567890',
        address: 'Keliling Kota',
        status: 'active'
      },
      {
        name: '<PERSON> B', 
        type: 'kangider',
        contact_person: '<PERSON><PERSON>',
        phone: '081234567891',
        address: 'Keliling Kota',
        status: 'active'
      },
      {
        name: 'SD Negeri 1',
        type: 'sekolah',
        contact_person: '<PERSON><PERSON>',
        phone: '081234567892',
        address: 'Jl. Pendidikan No. 1',
        status: 'active'
      },
      {
        name: 'SMP Negeri 2',
        type: 'sekolah',
        contact_person: '<PERSON>',
        phone: '081234567893',
        address: 'Jl. Pendidikan No. 2',
        status: 'active'
      },
      {
        name: 'SMK Negeri 1',
        type: 'sekolah',
        contact_person: '<PERSON><PERSON>',
        phone: '081234567894',
        address: 'Jl. Pendidikan No. 3',
        status: 'active'
      },
      {
        name: 'Reseller Utama',
        type: 'reseller',
        contact_person: 'Pak Dedi',
        phone: '081234567895',
        address: 'Jl. Perdagangan No. 1',
        status: 'active'
      },
      {
        name: 'Reseller Cabang',
        type: 'reseller',
        contact_person: 'Ibu Maya',
        phone: '081234567896',
        address: 'Jl. Perdagangan No. 2',
        status: 'active'
      },
      {
        name: 'Toko Kelontong A',
        type: 'reseller',
        contact_person: 'Pak Andi',
        phone: '081234567897',
        address: 'Jl. Raya No. 10',
        status: 'active'
      }
    ];
    
    // Check if distribution channels already exist
    const existingChannels = await directus.request(
      readItems("distribution_channels", {
        fields: ["id", "name"]
      })
    );
    
    console.log(`Found ${existingChannels.length} existing distribution channels`);
    
    const results = [];
    
    for (const channel of sampleChannels) {
      // Check if channel with same name already exists
      const exists = existingChannels.find(existing => existing.name === channel.name);
      
      if (!exists) {
        try {
          const newChannel = await directus.request(
            createItem("distribution_channels", {
              ...channel,
              created_at: new Date().toISOString()
            })
          );
          
          results.push({
            name: channel.name,
            type: channel.type,
            action: "created",
            id: newChannel.id
          });
          
          console.log(`✅ Created distribution channel: ${channel.name}`);
        } catch (error) {
          console.error(`❌ Failed to create channel ${channel.name}:`, error);
          results.push({
            name: channel.name,
            type: channel.type,
            action: "error",
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      } else {
        results.push({
          name: channel.name,
          type: channel.type,
          action: "exists",
          id: exists.id
        });
      }
    }
    
    console.log('✅ Distribution channels setup completed');
    
    return NextResponse.json({
      success: true,
      message: `Set up distribution channels: ${results.filter(r => r.action === 'created').length} created, ${results.filter(r => r.action === 'exists').length} already exist`,
      results
    });
    
  } catch (error) {
    console.error("❌ Error setting up distribution channels:", error);
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "Failed to set up distribution channels",
        error
      },
      { status: 500 }
    );
  }
}
