import { NextRequest, NextResponse } from 'next/server'

const DIRECTUS_URL = process.env.DIRECTUS_URL || 'http://localhost:8055'

export const dynamic = 'force-dynamic'

// GET - Fetch distribution channels
export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const { searchParams } = new URL(request.url)
    
    const type = searchParams.get('type') // kangider, sekolah, reseller
    const status = searchParams.get('status') || 'active'

    // Build filter
    const filters = [`filter[status][_eq]=${status}`]
    if (type) filters.push(`filter[type][_eq]=${type}`)

    const filterQuery = filters.length > 0 ? `?${filters.join('&')}&sort=name` : '?sort=name'

    console.log('🔄 Fetching distribution channels with filters:', filterQuery)

    const response = await fetch(`${DIRECTUS_URL}/items/distribution_channels${filterQuery}`, {
      headers: { 'Authorization': `Bearer ${token}` }
    })

    if (!response.ok) {
      throw new Error(`Directus API error: ${response.status}`)
    }

    const data = await response.json()
    
    return NextResponse.json({
      success: true,
      data: data.data || []
    })

  } catch (error) {
    console.error('❌ Error fetching distribution channels:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch distribution channels',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// POST - Create distribution channel
export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const token = authHeader.replace('Bearer ', '')
    const body = await request.json()
    const { name, type, contact_person, phone, address } = body

    console.log('🔄 Creating distribution channel:', { name, type })

    if (!name || !type) {
      return NextResponse.json(
        { success: false, error: 'Name and type are required' },
        { status: 400 }
      )
    }

    if (!['kangider', 'sekolah', 'reseller'].includes(type)) {
      return NextResponse.json(
        { success: false, error: 'Invalid type. Must be kangider, sekolah, or reseller' },
        { status: 400 }
      )
    }

    const channelData = {
      name,
      type,
      contact_person: contact_person || null,
      phone: phone || null,
      address: address || null,
      status: 'active',
      created_at: new Date().toISOString()
    }

    const response = await fetch(`${DIRECTUS_URL}/items/distribution_channels`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(channelData)
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(`Failed to create distribution channel: ${errorData.errors?.[0]?.message || 'Unknown error'}`)
    }

    const result = await response.json()

    console.log('✅ Distribution channel created successfully')

    return NextResponse.json({
      success: true,
      message: 'Distribution channel created successfully',
      data: result.data
    })

  } catch (error) {
    console.error('❌ Error creating distribution channel:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create distribution channel',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
